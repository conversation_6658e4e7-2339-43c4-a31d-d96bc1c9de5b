<?php namespace edgeTemplate\view_1903423eac53223db19138bc1ddba25d;use edge\Edge;?><?php ;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>
<div class="p-5">
  
  

  
  <div id="card-body">
    <?= Edge::render('forms-button', ["id" => "Get_Products", "label" => "Get Products", "hx-post" => "api/update_products", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"], 1) ?>

    <?= Edge::render('forms-button', ["id" => "Get_prices", "label" => "Update Prices", "hx-post" => "api/update_prices", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"], 1) ?>

    <?= Edge::render('forms-button', ["label" => "Get Subscriptions for api", "hx-post" => "api/subscriptions_get_from_api", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"], 1) ?>
    <?= Edge::render('forms-button', ["label" => "Get Quote JSON from api", "hx-post" => "api/quotes_get_from_api", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"], 1) ?>
    

    <?= Edge::render('forms-button', ["label" => "Get Promos", "hx-target" => "#output_card_body", "hx-swap" => "innerHtml", "hx-post" => "api/autodesk/get_promos_from_api"], 1) ?>

    <?= Edge::render('forms-form', ["id" => "autodesk_search_customers_form", "class" => "form-control", "hx-post" => "api_h.php", "hx-swap" => "innerHtml", "hx-target" => "#output_card_body"], 1) ?>

    <h3>Search Customers</h3>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "name", "id" => "name_input", "placeholder" => "name", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "countryCode", "id" => "countryCode_input", "placeholder" => "countryCode", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "contactEmail", "id" => "email_input", "placeholder" => "Email", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "subscriptionId", "id" => "subscriptionId_input", "placeholder" => "subscriptionId", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "endpoint", "id" => "endpoint_input", "placeholder" => "endpoint", "class" => "form-control"], 1) ?>
    <?= Edge::render('forms-button', ["label" => "Submit", "class" => "btn btn-primary"], 1) ?>
    <?= Edge::render('forms-form',null,2) ?><br><br>


    <h3>Get opportunity</h3>

    <?= Edge::render('forms-form', ["id" => "autodesk_get_opportunity_form", "hx-post" => "api_h.php", "hx-swap" => "innerHtml", "hx-target" => "#output_card_body"], 1) ?>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "endCustomerCsn", "id" => "endCustomerCsn_input", "placeholder" => "endCustomerCsn", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "opportunityNumber", "id" => "opportunityNumber_input", "placeholder" => "opportunityNumber", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-button', ["label" => "Submit", "class" => "btn btn-primary"], 1) ?>

    <?= Edge::render('forms-form',null,2) ?><br><br>
    <?= Edge::render('forms-form', ["id" => "autodesk_raw_api_call_form", "hx-post" => "api/autodesk_send_raw_api_call", "hx-swap" => "innerHtml", "hx-target" => "#output_card_card_body", "hx-vals" => ""], 1) ?>'
    >
    <?= Edge::render('forms-input', ["type" => "text", "name" => "requestType", "id" => "requestType_input", "placeholder" => "requestType (POST, GET)", "class" => "form-control"], 1) ?>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "endpoint", "id" => "endpoint_input", "placeholder" => "endpoint", "class" => "form-control"], 1) ?>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "downloadFilepath", "id" => "downloadFilepath_input", "placeholder" => "downloadFilepath", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-textarea', ["name" => "params", "id" => "params_textarea", "placeholder" => "params", "label" => "params (php assoc array)", "class" => "form-control"], 1) ?>

    <?= Edge::render('forms-textarea', ["name" => "json", "id" => "json", "placeholder" => "json here", "label" => "input (json)", "class" => "form-control"], 1) ?>

    <button type="submit">Send Raw</button>
    <?= Edge::render('forms-form',null,2) ?><br><br>


    <?= Edge::render('layout-card', ["id" => "output_card", "label" => "Output"], 1) ?>
    <?= Edge::render('layout-card',null,2) ?>
  </div>
</div>
<?php } ?>
<?php
/**
 * Final comprehensive test for constants caching
 */

// Define minimal constants for testing
define('DEBUG_MODE', true);
define('API_RUN', false);

// Include required files
require_once __DIR__ . '/system/functions/path_utils.php';
require_once __DIR__ . '/system/paths.php';

// Create test path array
$test_path = [
    'fs_app_root' => __DIR__ . '/',
    'fs_cache' => __DIR__ . '/cache',
    'fs_system' => __DIR__ . '/system/',
    'app_root' => '/autobooks/',
    'request_uri' => '/test',
    'domain' => 'localhost'
];

echo "Final Constants Caching Test\n";
echo "============================\n\n";

// Test 1: Clear cache and generate fresh
echo "1. Clearing cache and generating fresh constants...\n";
clear_constants_cache($test_path);

$start_time = microtime(true);
build_constants($test_path);
$generation_time = microtime(true) - $start_time;

$cache_file = get_constants_cache_file($test_path);
echo "   Generation time: " . number_format($generation_time * 1000, 2) . " ms\n";
echo "   Cache file exists: " . (file_exists($cache_file) ? 'YES' : 'NO') . "\n";
echo "   Cache file size: " . (file_exists($cache_file) ? filesize($cache_file) : 0) . " bytes\n\n";

// Test 2: Simulate fresh PHP process by unsetting constants
echo "2. Simulating fresh process (clearing constants from memory)...\n";
// We can't actually undefine constants in PHP, so we'll create a new process

// Test 3: Load from cache
echo "3. Testing cache loading in new process...\n";
$test_script = '<?php
define("DEBUG_MODE", true);
define("API_RUN", false);
require_once __DIR__ . "/system/functions/path_utils.php";
require_once __DIR__ . "/system/paths.php";

$test_path = [
    "fs_app_root" => __DIR__ . "/",
    "fs_cache" => __DIR__ . "/cache",
    "fs_system" => __DIR__ . "/system/",
    "app_root" => "/autobooks/",
    "request_uri" => "/test",
    "domain" => "localhost"
];

$start_time = microtime(true);
build_constants($test_path);
$load_time = microtime(true) - $start_time;

echo "Cache load time: " . number_format($load_time * 1000, 2) . " ms\n";
echo "FS_APP_ROOT defined: " . (defined("FS_APP_ROOT") ? "YES" : "NO") . "\n";
echo "FS_CACHE defined: " . (defined("FS_CACHE") ? "YES" : "NO") . "\n";
echo "APP_ROOT defined: " . (defined("APP_ROOT") ? "YES" : "NO") . "\n";
';

file_put_contents('temp_cache_test.php', $test_script);
$output = shell_exec('php temp_cache_test.php');
echo $output;
unlink('temp_cache_test.php');

// Test 4: Show cache file sample
echo "\n4. Cache file sample (first 10 lines):\n";
if (file_exists($cache_file)) {
    $lines = file($cache_file);
    $sample_lines = array_slice($lines, 0, 10);
    foreach ($sample_lines as $i => $line) {
        echo "   " . ($i + 1) . ": " . rtrim($line) . "\n";
    }
    if (count($lines) > 10) {
        echo "   ... (" . (count($lines) - 10) . " more lines)\n";
    }
}

// Test 5: Performance comparison
echo "\n5. Performance summary:\n";
echo "   Fresh generation: " . number_format($generation_time * 1000, 2) . " ms\n";
echo "   Cache benefits: Faster startup, IDE constant recognition\n";

echo "\n✅ All tests completed successfully!\n";
echo "\nBenefits of the caching system:\n";
echo "- Faster application startup (constants loaded from cache)\n";
echo "- IDE can parse the cache file for constant definitions\n";
echo "- Automatic cache invalidation when path data changes\n";
echo "- Fallback to dynamic generation if cache is missing/invalid\n";

<?php
/**
 * Simple test to verify cache file generation
 */

// Define minimal constants for testing
define('DEBUG_MODE', true);
define('API_RUN', false);

// Include required files
require_once __DIR__ . '/system/functions/path_utils.php';
require_once __DIR__ . '/system/paths.php';

// Create a simple test path array
$test_path = [
    'fs_app_root' => __DIR__ . '/',
    'fs_cache' => __DIR__ . '/cache',
    'fs_system' => __DIR__ . '/system/',
    'app_root' => '/autobooks/',
    'request_uri' => '/test',
    'domain' => 'localhost',
    'test_string' => 'hello world',
    'test_number' => 42,
    'test_bool' => true
];

echo "Simple Cache Test\n";
echo "=================\n\n";

// Clear any existing cache
echo "Clearing cache...\n";
clear_constants_cache($test_path);

// Generate cache
echo "Generating constants and cache...\n";
build_constants($test_path);

// Check cache file
$cache_file = get_constants_cache_file($test_path);
echo "Cache file: {$cache_file}\n";
echo "Cache file exists: " . (file_exists($cache_file) ? 'YES' : 'NO') . "\n";

if (file_exists($cache_file)) {
    echo "Cache file size: " . filesize($cache_file) . " bytes\n";
    echo "\nCache file contents:\n";
    echo "--------------------\n";
    echo file_get_contents($cache_file);
    echo "--------------------\n";
}

// Test some constants
echo "\nTesting constants:\n";
$test_constants = ['FS_APP_ROOT', 'FS_CACHE', 'APP_ROOT', 'TEST_STRING', 'TEST_NUMBER', 'TEST_BOOL'];
foreach ($test_constants as $const) {
    if (defined($const)) {
        $value = constant($const);
        $type = gettype($value);
        echo "✓ {$const} ({$type}) = ";
        if (is_bool($value)) {
            echo $value ? 'true' : 'false';
        } else {
            echo $value;
        }
        echo "\n";
    } else {
        echo "✗ {$const} is not defined\n";
    }
}

echo "\nTest completed!\n";

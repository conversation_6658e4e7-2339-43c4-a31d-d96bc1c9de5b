<?php
/**
 * Test script for constants caching functionality
 */

// Define minimal constants for testing
define('DEBUG_MODE', true);
define('API_RUN', false);

// Include required files first
require_once __DIR__ . '/system/functions/path_utils.php';
require_once __DIR__ . '/system/paths.php';

// Set up basic path structure - use build_paths to get realistic data
$path = ['fs_app_root' => __DIR__ . '/'];
$schema = require_once __DIR__ . '/system/config/path_schema.php';

// Simulate server environment
$_SERVER['REQUEST_URI'] = '/test';
$_SERVER['SERVER_NAME'] = 'localhost';
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['SCRIPT_FILENAME'] = __DIR__ . '/index.php';
$_SERVER['DOCUMENT_ROOT'] = dirname(__DIR__, 3);
$_SERVER['HTTPS'] = 'off';

$path = build_paths($path, $schema);

echo "Testing Constants Caching Functionality\n";
echo "========================================\n\n";

// Debug: Show path data
echo "Debug: Path data contains " . count($path) . " entries:\n";
foreach ($path as $key => $value) {
    if (is_string($value)) {
        echo "   {$key} => {$value}\n";
    } else {
        echo "   {$key} => " . gettype($value) . "\n";
    }
}
echo "\n";

// Test 1: Clear any existing cache
echo "1. Clearing existing cache...\n";
clear_constants_cache($path);
echo "   Cache cleared.\n\n";

// Test 2: First run - should generate cache
echo "2. First run (should generate cache)...\n";
$start_time = microtime(true);
build_constants($path);
$first_run_time = microtime(true) - $start_time;
echo "   Time taken: " . number_format($first_run_time * 1000, 2) . " ms\n";

// Check if cache file was created
$cache_file = get_constants_cache_file($path);
echo "   Cache file: " . $cache_file . "\n";
echo "   Cache file exists: " . (file_exists($cache_file) ? 'YES' : 'NO') . "\n\n";

// Test 3: Second run - should load from cache
echo "3. Second run (should load from cache)...\n";
// Clear the constants first to test loading from cache
$constants_to_clear = [];
foreach ($path as $key => $value) {
    $const_name = strtoupper($key);
    if (defined($const_name)) {
        $constants_to_clear[] = $const_name;
    }
}

$start_time = microtime(true);
build_constants($path);
$second_run_time = microtime(true) - $start_time;
echo "   Time taken: " . number_format($second_run_time * 1000, 2) . " ms\n";

// Test 4: Verify constants are defined
echo "\n4. Verifying constants are defined...\n";
$sample_constants = ['FS_APP_ROOT', 'FS_CACHE', 'FS_SYSTEM', 'APP_ROOT'];
foreach ($sample_constants as $const) {
    if (defined($const)) {
        echo "   ✓ {$const} = " . constant($const) . "\n";
    } else {
        echo "   ✗ {$const} is not defined\n";
    }
}

// Test 5: Show cache file contents
echo "\n5. Cache file contents (first 20 lines):\n";
if (file_exists($cache_file)) {
    $lines = file($cache_file);
    $display_lines = array_slice($lines, 0, 20);
    foreach ($display_lines as $i => $line) {
        echo "   " . ($i + 1) . ": " . rtrim($line) . "\n";
    }
    if (count($lines) > 20) {
        echo "   ... (" . (count($lines) - 20) . " more lines)\n";
    }
} else {
    echo "   Cache file not found!\n";
}

// Test 6: Performance comparison
echo "\n6. Performance comparison:\n";
if ($first_run_time > 0 && $second_run_time > 0) {
    $improvement = (($first_run_time - $second_run_time) / $first_run_time) * 100;
    echo "   First run:  " . number_format($first_run_time * 1000, 2) . " ms\n";
    echo "   Second run: " . number_format($second_run_time * 1000, 2) . " ms\n";
    echo "   Improvement: " . number_format($improvement, 1) . "%\n";
}

echo "\nTest completed!\n";
